<!DOCTYPE html>
<html>
  <head>
    <title>AI Marketing Hub</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- Shared Navigation -->
    <%= render 'shared/navbar' %>

    <!-- Flash Messages -->
    <%= render 'shared/flash_messages' %>

    <% if user_signed_in? %>
      <!-- Mobile Sidebar Overlay -->
      <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

      <!-- Main Layout with Sidebar -->
      <div class="flex h-screen pt-16">
        <!-- Sidebar -->
        <nav id="sidebar" class="fixed lg:static inset-y-0 left-0 z-50 w-64 lg:w-20 xl:w-64 bg-white shadow-lg border-r border-gray-200 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out">
          <div class="flex flex-col h-full pt-16 lg:pt-0">
            <!-- Sidebar Header (Mobile Only) -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
              <h2 class="text-lg font-semibold text-gray-900">Navigation</h2>
              <button id="sidebar-close" class="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- Navigation Links -->
            <div class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
              <%= link_to dashboard_path, class: "relative flex items-center px-3 py-3 rounded-lg transition-colors group #{ current_page?(dashboard_path) ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600' }" do %>
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Dashboard</span>
                <!-- Tooltip for compact mode -->
                <div class="sidebar-tooltip lg:block xl:hidden">Dashboard</div>
              <% end %>

              <%= link_to campaigns_path, class: "relative flex items-center px-3 py-3 rounded-lg transition-colors group #{ current_page?(campaigns_path) || controller_name == 'campaigns' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600' }" do %>
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Campaigns</span>
                <!-- Tooltip for compact mode -->
                <div class="sidebar-tooltip lg:block xl:hidden">Campaigns</div>
              <% end %>

              <div class="relative flex items-center px-3 py-3 text-gray-400 rounded-lg cursor-not-allowed">
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Audiences</span>
                <span class="ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full lg:hidden xl:block">Soon</span>
                <!-- Tooltip for compact mode -->
                <div class="sidebar-tooltip lg:block xl:hidden">Audiences (Coming Soon)</div>
              </div>

              <div class="relative flex items-center px-3 py-3 text-gray-400 rounded-lg cursor-not-allowed">
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Analytics</span>
                <span class="ml-auto text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full lg:hidden xl:block">Soon</span>
                <!-- Tooltip for compact mode -->
                <div class="sidebar-tooltip lg:block xl:hidden">Analytics (Coming Soon)</div>
              </div>
            </div>

            <!-- Sidebar Footer -->
            <div class="p-4 border-t border-gray-200">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span class="text-white text-sm font-semibold">
                    <%= current_user.email.first.upcase %>
                  </span>
                </div>
                <div class="ml-3 lg:hidden xl:block">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    <%= current_user.email.split('@').first %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= current_user.tenant.name if current_user.tenant %>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <!-- Main Content Area -->
        <main class="flex-1 lg:ml-20 xl:ml-64 min-h-screen bg-gray-50">
          <!-- Content Container with Responsive Padding -->
          <div class="h-full overflow-auto">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
              <%= yield %>
            </div>
          </div>
        </main>
      </div>
    <% else %>
      <!-- Non-authenticated layout -->
      <main class="pt-16 min-h-screen">
        <div class="h-full overflow-auto">
          <%= yield %>
        </div>
      </main>
    <% end %>
    
    <!-- Shared Footer -->
    <%= render 'shared/footer' %>
  </body>
</html>
