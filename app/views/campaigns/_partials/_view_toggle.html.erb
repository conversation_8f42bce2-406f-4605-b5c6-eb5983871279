<%# View Toggle Controls %>
<div class="campaigns-view-toggle bg-white backdrop-blur-md rounded-xl p-1 flex shadow-sm border border-gray-200">
  <button class="campaigns-view-btn active group flex-1 px-3 py-2 rounded-lg" data-view="grid" title="Grid View">
    <div class="flex items-center justify-center gap-2">
      <svg class="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
      </svg>
      <span class="hidden sm:inline text-sm">Grid</span>
    </div>
  </button>
  <button class="campaigns-view-btn group flex-1 px-3 py-2 rounded-lg" data-view="table" title="Table View">
    <div class="flex items-center justify-center gap-2">
      <svg class="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
      </svg>
      <span class="hidden sm:inline text-sm">Table</span>
    </div>
  </button>
  <button class="campaigns-view-btn group flex-1 px-3 py-2 rounded-lg" data-view="kanban" title="Kanban View">
    <div class="flex items-center justify-center gap-2">
      <svg class="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2v10a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2zM9 7a2 2 0 012-2h2a2 2 0 012 2v2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
      </svg>
      <span class="hidden sm:inline text-sm">Kanban</span>
    </div>
  </button>
</div>
