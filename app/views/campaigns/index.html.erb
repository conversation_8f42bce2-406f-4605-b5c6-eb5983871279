<% content_for :title, "Campaigns" %>
<div class="campaigns-bg-overlay"></div>

<!-- <PERSON> Header with Enhanced Design -->
<div class="relative mb-8 lg:mb-12">
  <div class="campaigns-header-card bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-6 lg:p-8 border border-white/20 backdrop-blur-sm">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="flex-1">
        <div class="flex items-center gap-4 mb-4">
          <div class="campaigns-icon-wrapper">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Campaign Center
            </h1>
            <p class="text-lg text-gray-600 mt-1">Orchestrate powerful marketing campaigns with precision and insight</p>
          </div>
        </div>
        
        <!-- Quick Stats Bar -->
        <div class="flex flex-wrap gap-4 text-sm">
          <div class="flex items-center gap-2 text-green-600">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="font-medium"><%= @campaign_stats[:active] %> Active</span>
          </div>
          <div class="flex items-center gap-2 text-blue-600">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span class="font-medium">$<%= number_with_delimiter(@campaigns.sum(:budget_cents) / 100) %> Total Budget</span>
          </div>
          <div class="flex items-center gap-2 text-purple-600">
            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span class="font-medium"><%= @campaign_stats[:total] %> Total Campaigns</span>
          </div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3">
        <%= link_to new_campaign_path, class: "campaigns-create-btn group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" do %>
          <div class="campaigns-btn-icon mr-3">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </div>
          <span>Create Campaign</span>
        <% end %>
        
        <button class="campaigns-analytics-btn group inline-flex items-center justify-center px-6 py-3 bg-white text-gray-700 font-semibold rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-1 transition-all duration-300">
          <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <span>Analytics</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Stats Dashboard -->
<div class="campaigns-stats-grid grid grid-cols-2 md:grid-cols-5 gap-4 lg:gap-6 mb-8 lg:mb-12">
  <!-- Total Campaigns -->
  <div class="campaigns-stat-card group" data-stat="total">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-gray-100 to-gray-200">
      <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number"><%= @campaign_stats[:total] %></p>
      <p class="campaigns-stat-label">Total Campaigns</p>
      <div class="campaigns-stat-trend">
        <span class="text-gray-500 text-xs">All campaigns</span>
      </div>
    </div>
  </div>

  <!-- Active Campaigns -->
  <div class="campaigns-stat-card group" data-stat="active">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-green-100 to-emerald-200">
      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-green-600"><%= @campaign_stats[:active] %></p>
      <p class="campaigns-stat-label">Active</p>
      <div class="campaigns-stat-trend">
        <span class="text-green-600 text-xs flex items-center gap-1">
          <div class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
          Live now
        </span>
      </div>
    </div>
  </div>

  <!-- Draft Campaigns -->
  <div class="campaigns-stat-card group" data-stat="draft">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-blue-100 to-indigo-200">
      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-blue-600"><%= @campaign_stats[:draft] %></p>
      <p class="campaigns-stat-label">Draft</p>
      <div class="campaigns-stat-trend">
        <span class="text-blue-600 text-xs">In progress</span>
      </div>
    </div>
  </div>

  <!-- Paused Campaigns -->
  <div class="campaigns-stat-card group" data-stat="paused">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-orange-100 to-amber-200">
      <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-orange-600"><%= @campaign_stats[:paused] %></p>
      <p class="campaigns-stat-label">Paused</p>
      <div class="campaigns-stat-trend">
        <span class="text-orange-600 text-xs">On hold</span>
      </div>
    </div>
  </div>

  <!-- Completed Campaigns -->
  <div class="campaigns-stat-card group" data-stat="completed">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-purple-100 to-violet-200">
      <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-purple-600"><%= @campaign_stats[:completed] %></p>
      <p class="campaigns-stat-label">Completed</p>
      <div class="campaigns-stat-trend">
        <span class="text-purple-600 text-xs">Finished</span>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Filters and Search -->
<div class="campaigns-filters-card bg-white/80 backdrop-blur-lg rounded-2xl shadow-lg border border-white/20 p-6 lg:p-8 mb-8 lg:mb-12">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-3">
      <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
        </svg>
      </div>
      Advanced Filters
    </h3>
    <button class="campaigns-filter-toggle text-sm text-gray-500 hover:text-gray-700 transition-colors">
      <span class="sr-only">Toggle filters</span>
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>
  </div>

  <%= form_with url: campaigns_path, method: :get, local: true, class: "campaigns-filter-form" do |form| %>
    <!-- Enhanced Search Bar -->
    <div class="campaigns-search-wrapper relative mb-6">
      <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      <%= form.text_field :search,
          placeholder: "Search campaigns by name, description, or target audience...",
          value: params[:search],
          class: "campaigns-search-input w-full pl-12 pr-4 py-4 text-lg border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50/50 transition-all duration-300" %>
      <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
        <kbd class="inline-flex items-center border border-gray-200 rounded px-2 py-1 text-xs font-sans font-medium text-gray-400">⌘K</kbd>
      </div>
    </div>

    <!-- Filter Grid -->
    <div class="campaigns-filters-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Status Filter -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Campaign Status</label>
        <%= form.select :status,
            options_for_select([
              ['All Statuses', ''],
              ['🟢 Active', 'active'],
              ['📝 Draft', 'draft'],
              ['⏸️ Paused', 'paused'],
              ['✅ Completed', 'completed'],
              ['❌ Cancelled', 'cancelled']
            ], params[:status]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>

      <!-- Type Filter -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Campaign Type</label>
        <%= form.select :type,
            options_for_select([
              ['All Types', ''],
              ['📧 Email Marketing', 'email'],
              ['📱 Social Media', 'social'],
              ['🔍 SEO Campaign', 'seo'],
              ['⚡ Multi-Channel', 'multi_channel']
            ], params[:type]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>

      <!-- Budget Range Filter -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Budget Range</label>
        <%= form.select :budget_range,
            options_for_select([
              ['All Budgets', ''],
              ['💰 Under $1,000', 'under_1000'],
              ['💸 $1,000 - $5,000', '1000_5000'],
              ['🏦 $5,000 - $10,000', '5000_10000'],
              ['🚀 Over $10,000', 'over_10000']
            ], params[:budget_range]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>

      <!-- Sort Options -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Sort By</label>
        <%= form.select :sort,
            options_for_select([
              ['📅 Most Recent', ''],
              ['📝 Name A-Z', 'name'],
              ['💰 Budget (High-Low)', 'budget_desc'],
              ['📈 Budget (Low-High)', 'budget_asc'],
              ['🚀 Start Date', 'start_date'],
              ['📊 Status', 'status']
            ], params[:sort]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="campaigns-filter-actions flex flex-wrap gap-3">
      <%= form.submit "Apply Filters",
          class: "campaigns-filter-btn campaigns-filter-btn-primary" %>
      <%= link_to "Clear All", campaigns_path,
          class: "campaigns-filter-btn campaigns-filter-btn-secondary" %>
      
      <!-- Quick Filter Pills -->
      <div class="campaigns-quick-filters flex flex-wrap gap-2 ml-auto">
        <%= link_to campaigns_path(status: 'active'), 
            class: "campaigns-quick-filter #{'active' if params[:status] == 'active'}" do %>
          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
          Active Only
        <% end %>
        <%= link_to campaigns_path(type: 'email'), 
            class: "campaigns-quick-filter #{'active' if params[:type] == 'email'}" do %>
          📧 Email
        <% end %>
        <%= link_to campaigns_path(sort: 'budget_desc'), 
            class: "campaigns-quick-filter #{'active' if params[:sort] == 'budget_desc'}" do %>
          💰 High Budget
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<!-- Enhanced Campaigns Display -->
<% if @campaigns.any? %>
  <!-- View Toggle -->
  <div class="campaigns-view-controls flex items-center justify-between mb-6">
    <div class="flex items-center gap-4">
      <h2 class="text-xl font-semibold text-gray-900">Campaign Library</h2>
      <span class="text-sm text-gray-500">(<%= pluralize(@campaigns.count, 'campaign') %> found)</span>
    </div>
    
    <div class="campaigns-view-toggle bg-gray-100 rounded-lg p-1 flex">
      <button class="campaigns-view-btn active" data-view="grid">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
        </svg>
        <span>Grid</span>
      </button>
      <button class="campaigns-view-btn" data-view="table">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
        </svg>
        <span>Table</span>
      </button>
      <button class="campaigns-view-btn" data-view="kanban">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
        </svg>
        <span>Kanban</span>
      </button>
    </div>
  </div>

  <!-- Grid View (Default) -->
  <div class="campaigns-grid-view active">
    <div class="campaigns-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      <% @campaigns.each_with_index do |campaign, index| %>
        <div class="campaigns-card group" data-campaign-id="<%= campaign.id %>" style="animation-delay: <%= index * 0.1 %>s">
          <!-- Campaign Header -->
          <div class="campaigns-card-header">
            <div class="flex items-start justify-between">
              <div class="campaigns-type-badge campaigns-type-<%= campaign.campaign_type %>">
                <% case campaign.campaign_type %>
                <% when 'email' %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                  </svg>
                  <span>Email</span>
                <% when 'social' %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                  </svg>
                  <span>Social</span>
                <% when 'seo' %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <span>SEO</span>
                <% when 'multi_channel' %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>Multi</span>
                <% end %>
              </div>
              
              <div class="campaigns-status-badge campaigns-status-<%= campaign.status %>">
                <% case campaign.status %>
                <% when 'active' %>
                  <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Live</span>
                <% when 'draft' %>
                  <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                  <span>Draft</span>
                <% when 'paused' %>
                  <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Paused</span>
                <% when 'completed' %>
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Done</span>
                <% when 'cancelled' %>
                  <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>Stopped</span>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Campaign Content -->
          <div class="campaigns-card-content">
            <%= link_to campaign_path(campaign), class: "campaigns-card-title-link" do %>
              <h3 class="campaigns-card-title"><%= truncate(campaign.name, length: 40) %></h3>
            <% end %>
            
            <p class="campaigns-card-description">
              <%= truncate(campaign.description || "No description available", length: 80) %>
            </p>

            <div class="campaigns-card-meta">
              <div class="campaigns-meta-item">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span><%= truncate(campaign.target_audience || "General audience", length: 20) %></span>
              </div>
              
              <div class="campaigns-meta-item">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                <span class="font-semibold">$<%= number_with_delimiter(campaign.budget_in_dollars, delimiter: ',') %></span>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="campaigns-progress-section">
              <div class="flex items-center justify-between text-sm mb-2">
                <span class="text-gray-600">Campaign Progress</span>
                <span class="font-semibold text-gray-900"><%= campaign.progress_percentage %>%</span>
              </div>
              <div class="campaigns-progress-bar">
                <div class="campaigns-progress-fill campaigns-progress-<%= campaign.status %>" 
                     style="width: <%= campaign.progress_percentage %>%"></div>
              </div>
            </div>

            <!-- Timeline -->
            <% if campaign.start_date || campaign.end_date %>
              <div class="campaigns-timeline">
                <div class="flex items-center gap-2 text-sm text-gray-500">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <% if campaign.start_date && campaign.end_date %>
                    <span><%= campaign.start_date.strftime("%b %d") %> - <%= campaign.end_date.strftime("%b %d, %Y") %></span>
                  <% elsif campaign.start_date %>
                    <span>Started <%= campaign.start_date.strftime("%b %d, %Y") %></span>
                  <% elsif campaign.end_date %>
                    <span>Ends <%= campaign.end_date.strftime("%b %d, %Y") %></span>
                  <% else %>
                    <span class="text-gray-400">No dates set</span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Campaign Actions -->
          <div class="campaigns-card-actions">
            <%= link_to campaign_path(campaign), class: "campaigns-action-btn campaigns-action-view" do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <span>View</span>
            <% end %>
            
            <%= link_to edit_campaign_path(campaign), class: "campaigns-action-btn campaigns-action-edit" do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              <span>Edit</span>
            <% end %>

            <div class="campaigns-action-dropdown relative">
              <button class="campaigns-action-btn campaigns-action-more" data-dropdown-toggle="campaign-<%= campaign.id %>-menu">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                </svg>
              </button>
              
              <div class="campaigns-dropdown-menu" id="campaign-<%= campaign.id %>-menu">
                <% if campaign.status == 'draft' %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, class: "campaigns-dropdown-item" do %>
                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                    </svg>
                    <span>Activate Campaign</span>
                  <% end %>
                <% elsif campaign.status == 'active' %>
                  <%= link_to pause_campaign_path(campaign), method: :patch, class: "campaigns-dropdown-item" do %>
                    <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Pause Campaign</span>
                  <% end %>
                <% elsif campaign.status == 'paused' %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, class: "campaigns-dropdown-item" do %>
                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                    </svg>
                    <span>Resume Campaign</span>
                  <% end %>
                <% end %>
                
                <div class="campaigns-dropdown-divider"></div>
                
                <%= link_to campaign_path(campaign), method: :delete, 
                    class: "campaigns-dropdown-item campaigns-dropdown-danger",
                    data: { confirm: "Are you sure you want to delete this campaign?" } do %>
                  <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  <span>Delete Campaign</span>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Table View -->
  <div class="campaigns-table-view">
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @campaigns.each do |campaign| %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600" %>
                    </div>
                    <div class="text-sm text-gray-500"><%= truncate(campaign.description, length: 50) %></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full 
                  <%= campaign.campaign_type == 'email' ? 'bg-blue-100 text-blue-800' : 
                      campaign.campaign_type == 'social' ? 'bg-green-100 text-green-800' : 
                      'bg-purple-100 text-purple-800' %>">
                  <%= campaign.campaign_type.capitalize %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full 
                  <%= campaign.status == 'active' ? 'bg-green-100 text-green-800' : 
                      campaign.status == 'paused' ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-gray-100 text-gray-800' %>">
                  <%= campaign.status.capitalize %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center space-x-2">
                  <span>CTR: <%= number_to_percentage(campaign.click_through_rate || 0, precision: 1) %></span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <%= link_to campaign_path(campaign), class: "text-blue-600 hover:text-blue-700" do %>
                    View
                  <% end %>
                  <%= link_to edit_campaign_path(campaign), class: "text-gray-600 hover:text-gray-700" do %>
                    Edit
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Mobile Card View -->
  <div class="responsive-cards space-y-4">
    <% @campaigns.each do |campaign| %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <!-- Campaign Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-1">
              <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600" %>
            </h3>
            <p class="text-sm text-gray-600 mb-2"><%= campaign.description %></p>
            <p class="text-xs text-gray-500">Target: <%= campaign.target_audience %></p>
          </div>

          <!-- Status Badge -->
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-4
            <%= case campaign.status
                when 'active' then 'bg-green-100 text-green-800'
                when 'draft' then 'bg-gray-100 text-gray-800'
                when 'paused' then 'bg-yellow-100 text-yellow-800'
                when 'completed' then 'bg-blue-100 text-blue-800'
                when 'cancelled' then 'bg-red-100 text-red-800'
                else 'bg-gray-100 text-gray-800'
                end %>">
            <%= campaign.status.titleize %>
          </span>
        </div>

        <!-- Campaign Details -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <!-- Type -->
          <div>
            <p class="text-xs text-gray-500 mb-1">Type</p>
            <div class="flex items-center">
              <% case campaign.campaign_type %>
              <% when 'email' %>
                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-blue-600 font-bold text-xs">@</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Email</span>
              <% when 'social' %>
                <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-green-600 font-bold text-xs">#</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Social</span>
              <% when 'seo' %>
                <div class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-purple-600 font-bold text-xs">S</span>
                </div>
                <span class="text-sm font-medium text-gray-900">SEO</span>
              <% when 'multi_channel' %>
                <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-orange-600 font-bold text-xs">⚡</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Multi-Channel</span>
              <% end %>
            </div>
          </div>

          <!-- Budget -->
          <div>
            <p class="text-xs text-gray-500 mb-1">Budget</p>
            <p class="text-sm font-medium text-gray-900">
              $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %>
            </p>
          </div>
        </div>

        <!-- Dates -->
        <div class="mb-4">
          <p class="text-xs text-gray-500 mb-1">Duration</p>
          <div class="text-sm text-gray-900">
            <% if campaign.start_date %>
              <%= campaign.start_date.strftime("%b %d, %Y") %>
              <% if campaign.end_date %>
                - <%= campaign.end_date.strftime("%b %d, %Y") %>
              <% end %>
            <% else %>
              <span class="text-gray-400">Dates not set</span>
            <% end %>
          </div>
        </div>

        <!-- Progress -->
        <div class="mb-4">
          <div class="flex items-center justify-between text-sm mb-2">
            <span class="text-gray-600">Progress</span>
            <span class="font-medium"><%= campaign.progress_percentage %>%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                 style="width: <%= campaign.progress_percentage %>%"></div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
          <div class="flex items-center space-x-3">
            <%= link_to campaign_path(campaign),
                class: "text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors",
                title: "View Campaign" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            <% end %>

            <%= link_to edit_campaign_path(campaign),
                class: "text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors",
                title: "Edit Campaign" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            <% end %>
          </div>

          <div class="flex items-center space-x-2">
            <!-- Status Action Button -->
            <% if campaign.can_be_activated? %>
              <%= link_to activate_campaign_path(campaign), method: :patch,
                  class: "px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full hover:bg-green-200 transition-colors",
                  data: { confirm: "Are you sure you want to activate this campaign?" } do %>
                Activate
              <% end %>
            <% elsif campaign.active? %>
              <%= link_to pause_campaign_path(campaign), method: :patch,
                  class: "px-3 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full hover:bg-yellow-200 transition-colors",
                  data: { confirm: "Are you sure you want to pause this campaign?" } do %>
                Pause
              <% end %>
            <% end %>

            <%= link_to campaign_path(campaign), method: :delete,
                class: "text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors",
                title: "Delete Campaign",
                data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
<% else %>
  <!-- Enhanced Empty State -->
  <div class="campaigns-empty-state">
    <div class="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-2xl shadow-lg border border-white/20 backdrop-blur-sm p-12 text-center">
      <div class="max-w-2xl mx-auto">
        <% if params[:search].present? || params[:status].present? || params[:type].present? %>
          <!-- Filtered Empty State -->
          <div class="campaigns-empty-icon w-32 h-32 bg-gradient-to-br from-orange-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg class="w-16 h-16 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
            </svg>
          </div>
          
          <h3 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-4">
            No Results Found
          </h3>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            No campaigns match your current filters. Try adjusting your search criteria or clearing filters to see all campaigns.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <%= link_to campaigns_path,
                class: "campaigns-clear-filters-btn inline-flex items-center px-8 py-4 bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Clear All Filters
            <% end %>
            
            <%= link_to new_campaign_path, class: "campaigns-create-secondary-btn inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-1 transition-all duration-300" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Create New Campaign
            <% end %>
          </div>
          
        <% else %>
          <!-- First Time Empty State with Quick Start Guide -->
          <div class="campaigns-empty-icon w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg class="w-16 h-16 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          
          <h3 class="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            Welcome to Campaign Center
          </h3>
          <p class="text-xl text-gray-600 mb-12 leading-relaxed">
            Launch powerful AI-driven marketing campaigns that convert prospects into customers with precision and intelligence.
          </p>
          
          <!-- Quick Start Guide -->
          <div class="campaigns-quick-start-grid grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 text-left">
            <div class="campaigns-quick-start-card bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/40 hover:shadow-lg transition-all duration-300">
              <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">1. Create Campaign</h4>
              <p class="text-gray-600 text-sm leading-relaxed">Set up your campaign goals, target audience, and budget parameters using our AI-powered recommendations.</p>
            </div>
            
            <div class="campaigns-quick-start-card bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/40 hover:shadow-lg transition-all duration-300">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">2. Launch & Monitor</h4>
              <p class="text-gray-600 text-sm leading-relaxed">Deploy your campaign across multiple channels and track real-time performance with advanced analytics.</p>
            </div>
            
            <div class="campaigns-quick-start-card bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/40 hover:shadow-lg transition-all duration-300">
              <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">3. Optimize Results</h4>
              <p class="text-gray-600 text-sm leading-relaxed">Use AI insights to continuously improve performance and maximize your return on investment.</p>
            </div>
          </div>
          
          <!-- Primary CTA -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <%= link_to new_campaign_path, class: "campaigns-create-primary-btn group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" do %>
              <svg class="w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <span>Create Your First Campaign</span>
              <div class="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </div>
            <% end %>
            
            <button class="campaigns-tour-btn inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-1 transition-all duration-300">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Take a Quick Tour
            </button>
          </div>
          
        <% end %>
      </div>
    </div>
  </div>
<% end %>

<!-- Pagination -->
<% if defined?(@pagy) && @pagy.pages > 1 %>
  <div class="mt-8 flex justify-center">
    <nav class="flex items-center space-x-2">
      <!-- Previous Page -->
      <% if @pagy.prev %>
        <%= link_to campaigns_path(page: @pagy.prev, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort]), class: "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" do %>
          Previous
        <% end %>
      <% else %>
        <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
          Previous
        </span>
      <% end %>

      <!-- Page Numbers -->
      <% @pagy.series.each do |item| %>
        <% if item.is_a?(Integer) %>
          <% if item == @pagy.page %>
            <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
              <%= item %>
            </span>
          <% else %>
            <%= link_to campaigns_path(page: item, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort]), class: "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" do %>
              <%= item %>
            <% end %>
          <% end %>
        <% elsif item == :gap %>
          <span class="px-3 py-2 text-sm font-medium text-gray-300">...</span>
        <% end %>
      <% end %>

      <!-- Next Page -->
      <% if @pagy.next %>
        <%= link_to campaigns_path(page: @pagy.next, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort]), class: "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" do %>
          Next
        <% end %>
      <% else %>
        <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
          Next
        </span>
      <% end %>
    </nav>
  </div>
<% end %>
